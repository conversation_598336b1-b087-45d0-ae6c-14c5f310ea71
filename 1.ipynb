import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import lightgbm as lgb
from sklearn.model_selection import TimeSeriesSplit
import xgboost as xgb
from sklearn.multioutput import MultiOutputRegressor # <-- Import the wrapper

df = pd.read_csv('dataCarSale2021-2025.csv')

df['waktu'] = pd.to_datetime(df['waktu'])
df = df.set_index('waktu')

df.info()
print(df.describe())
print(df.head(60))

df.plot(figsize=(15, 8), linestyle='-')
plt.title('Monthly Car Sales Trend by Manufacturer (2021-2025)', fontsize=16)
plt.xlabel('Date', fontsize=12)
plt.ylabel('Sales Volume', fontsize=12)
plt.legend(title='Manufacturer')
plt.grid(True)
plt.show()

# Calculate total sales to use as the target variable
df['TOTAL_SALES'] = df.sum(axis=1)

def create_features(df):
    """Function to create time-series features from a datetime index."""
    df['year'] = df.index.year
    df['month'] = df.index.month
    df['quarter'] = df.index.quarter
    df['dayofyear'] = df.index.dayofyear
    df['weekofyear'] = df.index.isocalendar().week.astype(int)
    return df

df = create_features(df)

FEATURES = ['year', 'month', 'quarter', 'dayofyear', 'weekofyear']
TARGETS = ['DAIHATSU', 'HONDA', 'MITSUBISHI', 'SUZUKI', 'TOYOTA'] # <-- Plural for clarity

X = df[FEATURES]
y = df[TARGETS]

print(df.head(20))


# --- Data Splitting ---
TRAIN_END = '2024-12-31'
X_train, y_train = X.loc[X.index <= TRAIN_END], y.loc[y.index <= TRAIN_END]
X_val, y_val = X.loc[X.index > TRAIN_END], y.loc[y.index > TRAIN_END]

print(f"Training data period: {X_train.index.min()} to {X_train.index.max()}")
print(f"Validation data period: {X_val.index.min()} to {X_val.index.max()}")

# --- Model Definition and Training ---
# 1. Define the base XGBoost model
base_model = xgb.XGBRegressor(
    objective='reg:squarederror',
    n_estimators=1000,
    learning_rate=0.05,
    max_depth=3,
    random_state=42,
    early_stopping_rounds=50
)

# 2. Wrap the base model with MultiOutputRegressor
model = MultiOutputRegressor(estimator=base_model)

print("\nTraining Multi-Output XGBoost model...")
# Note: MultiOutputRegressor doesn't directly support eval_set for early stopping in its `fit` method.
# The early stopping will be applied to each individual model inside the wrapper.
model.fit(X_train, y_train, eval_set=[(X_val, y_val)], verbose=False)

# Define the SMAPE function
def smape(y_true, y_pred):
    """Function to calculate SMAPE"""
    numerator = np.abs(y_pred - y_true)
    denominator = (np.abs(y_true) + np.abs(y_pred)) / 2
    # Avoid division by zero
    denominator[denominator == 0] = 1e-9
    return np.mean(numerator / denominator) * 100

# Predict on the validation set
val_preds = model.predict(X_val)

# Calculate the SMAPE score
smape_score = smape(y_val, val_preds)
print(f'SMAPE Score on Validation Data: {smape_score:.4f}')

# Visualize the prediction results
results = pd.DataFrame({'Actual': y_val, 'Predicted': val_preds}, index=y_val.index)

df[TARGET].plot(figsize=(15, 8), legend=True, label='Actual Sales')
results['Predicted'].plot(legend=True, label='Predicted Sales', style='--')
plt.title('Actual vs. Predicted Sales (XGBoost)', fontsize=16)
plt.show()

# --- Predict the future date ---
future_date = pd.to_datetime('2025-08-01')
future_df = pd.DataFrame(index=[future_date])
# Assuming create_features function is defined from previous cells
future_df = create_features(future_df)
X_future = future_df[FEATURES]
prediction = model.predict(X_future)
print(f"\nPredicted Total Sales for August 1, 2025: {prediction[0]:,.0f} units")

